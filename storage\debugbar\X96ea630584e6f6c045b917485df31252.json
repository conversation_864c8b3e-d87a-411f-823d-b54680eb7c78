{"__meta": {"id": "X96ea630584e6f6c045b917485df31252", "datetime": "2025-06-30 19:01:16", "utime": **********.274105, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751310075.790475, "end": **********.274124, "duration": 0.48364901542663574, "duration_str": "484ms", "measures": [{"label": "Booting", "start": 1751310075.790475, "relative_start": 0, "end": **********.211769, "relative_end": **********.211769, "duration": 0.4212942123413086, "duration_str": "421ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.211778, "relative_start": 0.42130303382873535, "end": **********.274127, "relative_end": 3.0994415283203125e-06, "duration": 0.06234908103942871, "duration_str": "62.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041568, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0033, "accumulated_duration_str": "3.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2450051, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.788}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.257518, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.788, "width_percent": 10.909}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.264892, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.697, "width_percent": 20.303}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "FzV7IxHutMHO2bLABZqHFUL7MNtRBR7qIeUxzlkC", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1015901021 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1015901021\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2094628621 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2094628621\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-901221492 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FzV7IxHutMHO2bLABZqHFUL7MNtRBR7qIeUxzlkC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-901221492\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1077659763 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1bujxd%7C1751309663468%7C9%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImEvdEprT0FGZnErMmF6cVRXem53VFE9PSIsInZhbHVlIjoiYjM3VG54ZStzdXM3amNwWjhxd1Jhb0R3YXkrRElWYkowZTMzenArNTdJL0Y5RVExTytFOXhyOTV6dHlYNmRzdkVXLzZDNmlON2RPMFdEcGE2MWpVUFJzOTZmbmt0YWtJS1B1Y2NHTi8vOERWQW03TGFIcDU4bjJDQ0NHMkR4UGJVM1FYSFcweEJyaTdEYXR2bkZFQVlUUUdnNnAvOXQrcVllWmJZSzNSa2JBZmNjRUgwTGxKTnpzRElLOUxGY3hGaW0wQTJWQldKMmEwVDBEcFc2clBIcVhSRHpHQXU3UVZjOWxIbmJkckh5cUtiQnNVcGpvU21KR1JKNWIrRUF2VW1QdUhBKzBxbmJqQmdXbm9reERvZk41dFJrdy8wZllFeW8xdUVwRUVLaFV6V2MxK01ob0xPdHphak1oR096amtUbjFwam9LUTFGcnRpS2V2blJCeTBnUGFISktrKzFyOVpnc2xWRUZvSEE0bFBXREY5T0l1dnJhTkprN01RN3loMkFDL1A5ODVwUE5lcnJ6TlZMUlFDL0Q1VEZGcnl3a1JjUGlERXoyeHlra0p2QVM2Qkg0QXhWbTVXMS9iM2pBQUpYTjM0MWlXbnpKSnhLdWhoN1dGQ3ZhcXBwOE1oSXRGUVhYaWRQYnBZcFNZZlJvUzY0VDNRbVFkM0g0L05TWDAiLCJtYWMiOiI1MjYyNjc2NDllN2RiZTZhOGJhNzlhZjc1MDkyNGJiYjQ3ZDM1YzIxY2FkYzliZGU2NDI0NDk0YzczNjkzOWE4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjZDcWxGK2dzTURSWFY5R1BjTmM3Wnc9PSIsInZhbHVlIjoibitBSWFuSGxUYUpiQUtBbUlDdVJzNWpZUDR1L2w2SG1aMXRnaktTL1V5YUVCUSs3UGk2dGU5VXdHOFVSMk1lclRaYTRNWG1uNzE4MVRYME1CTXN1cTBxaE9mcG9MMFV4OTBObVN5VzB5SVp0dnlzN1g3NFA0enlMaWdYN2Q3Yi9HalNFVFRDZkFMcHN6ODk2UXRtRDJJb0F4RGg0WDNJSUdOdGtFV2QzZ1pSTHZ6blkzN01HYldPMWk1aWFSVHlLN2RROEprWm84V21CdjdNZUNnaDVkYmlWb2tXSjNpQXNSNk1iMmZCSDMvNmh4WDFvWHd0ekNWUXpSK1N5ZEszTzBqQU42UmZPZ1JlaTVjanJCd1lCT1hvZDZwNTBwK1hGZDhQSWw2VEJWV21iR1FIcUxTWHNWMUJIN29JL0I3MVFQRlJzMXFGTXpuMkVXc3VNcUEzclRFczZSOEZNdlBnWWRxVmliLzQvb2tSZCtLVDNnRC8zcVpHV1Y2VmZBTDlITFBrY3prU3lhWEJzK0dveVUwRzQ2UUxrQUdCdkNPYi81Ly9tM3Z5Y01HM0NsVzBaSnRidG5DZThxTTZwVVExU05vN0JnTktHK2t5YmJJMlpIRXNYdk9qV2x3Q2tGUEhvaHBmUS8vY3p2YVpVdTR3ZW5BUGpTYkduSEx5N1EvUTIiLCJtYWMiOiIwYzNkN2JmNDYwYmY2YjRkZjA4OWM3YWFhMzAyODUwMDYyNmI2MzdiNThlZWM2NTIzNDVlZjMyYjIyMDY3OWJmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1077659763\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-605767716 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FzV7IxHutMHO2bLABZqHFUL7MNtRBR7qIeUxzlkC</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ItgBYbuMCIK2tVeknPfSFEKDbiqeLu0VRJnQpMxQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-605767716\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1905036724 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 19:01:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRDT3JFUmUrYmFjVStXa2cyVkx4L1E9PSIsInZhbHVlIjoiY2dFZzZTVitCTkluNHJPL1ArRExIa2V5ckk0dEpwNW5pRzVqUkhkckVUVWE3T0pxc1IzVmNnZTl1WjlBMW9JSlBMVEVndWRPNzZ6TnJjekcxTGNuS0laemQ3QUdEQit5a2g5ZnZMbVdMQmh6RG41NjVuTnZwek54K3Q1RE5SK2V2VmJrSXNkZkN3Zmp3Vm5RR21XbjJsVFN6Qm9CK1BXcDV3eTJ2cVc0UStOdHRZaUR2YVlhL25BcTRMNG55RkM3eUMrVFNNTEV0NXQ2YXdMRnpGUGhhanAyQXR4eUt6clhFZjhva1FWNWRiYTgvV3owNE9Ua3JaWUtWb29LdU14VHpIc2hyMFMxK2tWYXpKRzhscTBaWTZ1K2RjV2d2ZU8wVWl2OTZYeEJjUmhkaVljZitQNDMvS0lVR1FsQkpjbFFuK0lWbkI1Q1VJUmlzakJvYmNyNkl5MnBVbEVoU0YrTkdyWERKUFo2TkkxRTJBaWY0c2YxWUMvK0tNQ0dtRExhaWlpeVJkWkVENVRMSXhQWi9hTC9xTVRiYlJGeU1vdUp5cnZpQ0RCd3Q1cG5OdUp1RmFYS1hSNlZZYTBaaVFHRVNRU1AwVTAvM0YvQkVGV1ozZTJCSXJKQmUxZFdBSlFnSDM5TzVKckFJYkgwV281VWsxL1BPUUV6bUFLZEd0a0siLCJtYWMiOiI3NTY3MjIyNjZjZjMwYWViZTdlZWI3NDY3NDY1Mjg2ZDgyNjQ3OWJiNDQ5NmU3ZjJhYTRmYTBmZmU2MTZjMmEwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 21:01:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InA4V0ZzVlB4L3pJYVllek1vdU05Rmc9PSIsInZhbHVlIjoiaDczWEZIN2VuM003aTU3RytrQjl0Z0RWTDdsZWhpWWJDenFXWjNSUE9NRTh1aCsyVUdValNCUzNHOU9TdzhnMVN2bU1wVDdheEswU2JJa0h2QkhhRTN1RDlGb0lWVmcrVHFBMHJyV0R4Q3IxWHdCdmFkMU1KSStsMjJ3WEFFR28zbjBsaFF0S01vU2pNT0w1cmdReEpHb2MwRllTVUx5UmJ0QkVHUkgwR290QjdsNU5Rdy82VGNXSE9TM3RGelhvanNndHU1dTl2MUl1alFlRmdPS2VhRDE4WisyTnpaOHRMOE5kTFc3b2hYRm9ObkR0NWNFbmttZ0k4WVZqK0dwT0ZaNGFYUmt0azBLZi82c05BS1dVemVpblB6WG1jOWtGeVNKYnZKcmgwZVYrZWx2RDVrblJPSE5KOUs2RlNuS1E5VDN3ZWdQWHBDRkFsWDJCZWdDUHZ6Vm42a1ZaTmt6WkVEK1ZTa3BoZEFhUVBNWXFjVHUyaEt0T1BDemV0dUY5UlpVREJ2dmUwWElnSkRhVjlZZGVMRHRSaHRheU9WZHJxbjhhWlNlYkxuWDZVVXpUWXM4L0VZQ2h3L0RHZ3pZR0RoMk1QaTNSSFgyamM1emk0VnBhYlpvMTJSWmV5a3ExVGU4TnZtK0tDOFcyRGhKc2NJWW5UODhBbjc0ZjJlSGoiLCJtYWMiOiI2ZjExODQ4NWRmMDk2Mjk2OTg3NjA2YzQ1ZmQxOGQ3NzdmYWJiZDc4OGRhODczNmVhM2Q4YmVhZWM1NmVhOGIwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 21:01:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRDT3JFUmUrYmFjVStXa2cyVkx4L1E9PSIsInZhbHVlIjoiY2dFZzZTVitCTkluNHJPL1ArRExIa2V5ckk0dEpwNW5pRzVqUkhkckVUVWE3T0pxc1IzVmNnZTl1WjlBMW9JSlBMVEVndWRPNzZ6TnJjekcxTGNuS0laemQ3QUdEQit5a2g5ZnZMbVdMQmh6RG41NjVuTnZwek54K3Q1RE5SK2V2VmJrSXNkZkN3Zmp3Vm5RR21XbjJsVFN6Qm9CK1BXcDV3eTJ2cVc0UStOdHRZaUR2YVlhL25BcTRMNG55RkM3eUMrVFNNTEV0NXQ2YXdMRnpGUGhhanAyQXR4eUt6clhFZjhva1FWNWRiYTgvV3owNE9Ua3JaWUtWb29LdU14VHpIc2hyMFMxK2tWYXpKRzhscTBaWTZ1K2RjV2d2ZU8wVWl2OTZYeEJjUmhkaVljZitQNDMvS0lVR1FsQkpjbFFuK0lWbkI1Q1VJUmlzakJvYmNyNkl5MnBVbEVoU0YrTkdyWERKUFo2TkkxRTJBaWY0c2YxWUMvK0tNQ0dtRExhaWlpeVJkWkVENVRMSXhQWi9hTC9xTVRiYlJGeU1vdUp5cnZpQ0RCd3Q1cG5OdUp1RmFYS1hSNlZZYTBaaVFHRVNRU1AwVTAvM0YvQkVGV1ozZTJCSXJKQmUxZFdBSlFnSDM5TzVKckFJYkgwV281VWsxL1BPUUV6bUFLZEd0a0siLCJtYWMiOiI3NTY3MjIyNjZjZjMwYWViZTdlZWI3NDY3NDY1Mjg2ZDgyNjQ3OWJiNDQ5NmU3ZjJhYTRmYTBmZmU2MTZjMmEwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 21:01:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InA4V0ZzVlB4L3pJYVllek1vdU05Rmc9PSIsInZhbHVlIjoiaDczWEZIN2VuM003aTU3RytrQjl0Z0RWTDdsZWhpWWJDenFXWjNSUE9NRTh1aCsyVUdValNCUzNHOU9TdzhnMVN2bU1wVDdheEswU2JJa0h2QkhhRTN1RDlGb0lWVmcrVHFBMHJyV0R4Q3IxWHdCdmFkMU1KSStsMjJ3WEFFR28zbjBsaFF0S01vU2pNT0w1cmdReEpHb2MwRllTVUx5UmJ0QkVHUkgwR290QjdsNU5Rdy82VGNXSE9TM3RGelhvanNndHU1dTl2MUl1alFlRmdPS2VhRDE4WisyTnpaOHRMOE5kTFc3b2hYRm9ObkR0NWNFbmttZ0k4WVZqK0dwT0ZaNGFYUmt0azBLZi82c05BS1dVemVpblB6WG1jOWtGeVNKYnZKcmgwZVYrZWx2RDVrblJPSE5KOUs2RlNuS1E5VDN3ZWdQWHBDRkFsWDJCZWdDUHZ6Vm42a1ZaTmt6WkVEK1ZTa3BoZEFhUVBNWXFjVHUyaEt0T1BDemV0dUY5UlpVREJ2dmUwWElnSkRhVjlZZGVMRHRSaHRheU9WZHJxbjhhWlNlYkxuWDZVVXpUWXM4L0VZQ2h3L0RHZ3pZR0RoMk1QaTNSSFgyamM1emk0VnBhYlpvMTJSWmV5a3ExVGU4TnZtK0tDOFcyRGhKc2NJWW5UODhBbjc0ZjJlSGoiLCJtYWMiOiI2ZjExODQ4NWRmMDk2Mjk2OTg3NjA2YzQ1ZmQxOGQ3NzdmYWJiZDc4OGRhODczNmVhM2Q4YmVhZWM1NmVhOGIwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 21:01:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1905036724\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1930759014 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FzV7IxHutMHO2bLABZqHFUL7MNtRBR7qIeUxzlkC</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1930759014\", {\"maxDepth\":0})</script>\n"}}